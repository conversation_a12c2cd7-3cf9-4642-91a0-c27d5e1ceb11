export const allColumn = [
  { prop: "appName", label: "应用名称", minWidth: "100px" },
  { prop: "source", label: "问题来源", minWidth: "180px" },
  { prop: "faultCategory", label: "问题分类", width: "100px" },
  { prop: "priority", label: "优先级", width: "80px" },
  { prop: "description", label: "问题描述", minWidth: "160px" }, // TOP声音/问题
  { prop: "levelFunction", label: "L3/6功能", minWidth: "120px" },
  { prop: "issueAttribution", label: "主责任方", width: "100px", },
  { prop: "currentHandler", label: "当前处理人", width: "160px" },
  { prop: "progress", label: "问题进展", width: "100px" },
  { prop: "planSolveTime", label: "计划解决时间", width: "130px", transTime: true, },
  { prop: "totalVolume", label: "声量值", width: "90px" },
  { prop: "status", label: "当前阶段", minWidth: "100px", tooltip: "应用档案表中的字段映射关系：Close/Close(重复) -> 问题关闭；open -> 计划锁定中；Tracking -> 问题修复中" },
  { prop: "responsibleTeam", label: "责任团队/TL", width: "150px" },
  { prop: "owner", label: "责任人", width: "150px" },
  { prop: "opinionIssueLevel", label: "问题等级", minWidth: "180px" },
  { prop: "opinionIssueId", label: "问题单号", minWidth: "180px" },
  { prop: "represent", label: "归属代表处/系统部", width: "150px" },
  { prop: "reportedPerson", label: "提出人", width: "100px" },
  { prop: "productModel", label: "产品机型", width: "120px" },
  { prop: "suggestResolveTime", label: "建议解决时间", width: "130px", transTime: true },
  { prop: "remark", label: "备注", width: "130px", },
  { prop: "team", label: "纵队", minWidth: "100px" },
  { prop: "appPriority", label: "应用层级", minWidth: "100px" },
  { prop: "createTime", label: "创建时间", width: "130px", transTime: true, },
  // { prop: "", label: "EWP责任人", minWidth: "100px" },
  { prop: "packageName", label: "应用包名", minWidth: "100px" },
  { prop: "appLevel", label: "应用等级", minWidth: "100px" },
  { prop: "sceneName", label: "场景名称", minWidth: "100px" },
  { prop: "severity", label: "严重程度", minWidth: "180px" },
  // { prop: "", label: "关联子单", minWidth: "180px" },
  { prop: "weknowId", label: "weknow知识ID", minWidth: "180px" },
  // { prop: "", label: "工单状态", minWidth: "180px" },
  // { prop: "", label: "来源是否闭环", minWidth: "180px" },
  // { prop: "", label: "关联单类型", minWidth: "180px" },
  // { prop: "", label: "关联单号", minWidth: "180px" },
  // { prop: "", label: "关联单状态", minWidth: "180px" },
  // { prop: "", label: "问题进展", minWidth: "180px" },
  { prop: "productType", label: "产品类型", width: "120px" },

  // { prop: "", label: "问题分类", width: "100px" },
  // { prop: "", label: "复现概率", width: "100px" },
  // { prop: "", label: "复现路径", width: "100px" },
  // { prop: "", label: "复现应用版本", width: "100px" },
  // { prop: "", label: "复现OS版本", width: "100px" },
  // { prop: "", label: "应用问题修复版本", width: "100px" },
  // { prop: "", label: "依赖OS问题修复版本", width: "100px" },
  // { prop: "", label: "故障知识ID", width: "100px" },
  // { prop: "", label: "当前级别", width: "100px" },
  // { prop: "", label: "最高级别", width: "100px" },
  // { prop: "", label: "定界超期标记", width: "100px" },
  { prop: "delimitationCostTime", label: "定界耗时", minWidth: "100px" },
  // { prop: "", label: "修复耗时", width: "100px" },
  // { prop: "", label: "问题创建人", width: "100px" },
  // { prop: "", label: "定界责任人", width: "100px" },
  { prop: "appPm", label: "应用PM", minWidth: "100px" },
  { prop: "dtseLeader", label: "DTSE Leader", width: "150px" },
  { prop: "dtseOwner", label: "DTSE责任人", width: "100px" },
  { prop: "bdLeader", label: "BD Leader", width: "100px" },
  { prop: "bdOwner", label: "BD责任人", width: "100px" },
  { prop: "solutionLeader", label: "生态解决方案Leader", width: "150px" },
  { prop: "solutionOwner", label: "生态解决方案责任人", width: "150px" },
  { prop: "identifyFinishTime", label: "问题识别完成时间", minWidth: "130px", transTime: true },
  { prop: "delimitFinishTime", label: "问题定界完成时间", minWidth: "130px", transTime: true },
  { prop: "reviewDelimitTime", label: "问题审核完成时间", minWidth: "130px", transTime: true },
  { prop: "planLockTime", label: "计划锁定完成时间", minWidth: "130px", transTime: true },
  { prop: "repairFinishTime", label: "确认修复完成时间", minWidth: "130px", transTime: true },
  // { prop: "", label: "计划锁定完成时间", width: "130px", transTime: true },
  // { prop: "", label: "问题修复完成时间", width: "130px", transTime: true },
  { prop: "closeTime", label: "问题关闭时间", minWidth: "130px", transTime: true },
  // { prop: "", label: "是否关键问题", width: "100px" },
  // { prop: "", label: "问题提出人", width: "100px" },
  { prop: "relatedIssueId", label: "DTS单号/IR单号", width: "150px" },


];

export const defaultColumn = [
  { prop: "appName", label: "应用名称", minWidth: "100px" },
  { prop: "source", label: "问题来源", width: "80px" },
  { prop: "faultCategory", label: "问题分类", width: "100px" },
  { prop: "priority", label: "优先级", width: "90px", sortable: 'custom', sortOrders: ['descending', 'ascending', null] },
  { prop: "description", label: "问题描述", minWidth: "200px" }, // TOP声音/问题
  { prop: "levelFunction", label: "L3/6功能", minWidth: "100px" },
  { prop: "issueAttribution", label: "主责任方", width: "100px", },
  { prop: "currentHandler", label: "当前处理人", width: "100px" }, // HW责任人
  { prop: "progress", label: "问题进展", width: "300px", tooltip: '六组一队成员双击可以更新进展' }, // 计划与进展
  {
    prop: "planSolveTime",
    label: "计划解决时间", // 上线时间
    width: "130px",
    transTime: true,
  },
  { prop: "totalVolume", label: "声量值", width: "90px", sortable: 'custom', sortOrders: ['descending', 'ascending', null] },
  { prop: "status", label: "当前阶段", width: "100px", tooltip: "应用档案表中的字段映射关系：Close/Close(重复) -> 问题关闭；open -> 计划锁定中；Tracking -> 问题修复中" }, // 状态
  { prop: "responsibleTeam", label: "责任团队/TL", width: "120px", tooltip: '伙伴/系统侧责任人的Leader' }, // 修改问题的团队
  { prop: "owner", label: "责任人", width: "130px", tooltip: '伙伴/系统侧责任人' }, // 修改问题负责人
  { prop: "opinionIssueLevel", label: "问题等级", minWidth: "100px" },
  { prop: "opinionIssueId", label: "问题单号", minWidth: "180px" },
  { prop: "represent", label: "归属代表处/系统部", width: "150px" },
  { prop: "reportedPerson", label: "提出人", width: "100px" },
  { prop: "productModel", label: "产品机型", width: "120px" }, // 所属产品
  { prop: "suggestResolveTime", label: "建议解决时间", width: "120px", transTime: true, },
  { prop: "remark", label: "备注", width: "100px", },

];
