<template>
  <div class="opinion-manage">
    <div class="page-title-section">
      <div class="page-title">问题管理</div>
    </div>
    <!-- 筛选部分 -->
    <div class="filter-comp">
      <searchArea
        :defaulList="defaulList"
        :filterOptions="filterOptions"
        :otherList="otherList"
        @updateFilterOptions="handleFilterOptions"
      ></searchArea>
      <div class="table-head">
        <div class="table-header-row">
          <div class="table-head-left">
            <el-input
              class="filter-item"
              v-model="filterObj.appName"
              placeholder="请输入应用名称"
              clearable
              @change="getDataList"
            >
            </el-input>
            <el-input
              class="filter-item ml-8"
              v-model="filterObj.description"
              placeholder="请输入问题描述"
              clearable
              @change="getDataList"
            >
            </el-input>
            <el-button
              text
              type="primary"
              @click="showAllInputFilter = !showAllInputFilter"
              class="ml-8"
            >
              <span style="width: 50px">{{
                showAllInputFilter ? "收起" : "更多搜索"
              }}</span>
              <el-icon class="el-icon--right" v-if="showAllInputFilter"
                ><CaretTop
              /></el-icon>
              <el-icon v-if="!showAllInputFilter" class="el-icon--right"
                ><CaretBottom
              /></el-icon>
            </el-button>
            <el-button type="primary" class="ml-8" @click="getDataList"
              >查询</el-button
            >
            <el-button @click="resetSearch">重置</el-button>
          </div>
          <div class="table-head-right">
            <el-button
              :loading="exportLoading"
              @click="handleExportRes"
              v-if="hasExportPermission"
              >导出</el-button
            >
            <ColumnSettingDialog
              :allColumn="allColumn"
              :defaultColumn="defaultColumn"
              :localStorageName="LocalStorageEnum.ISSUES_OPTIONION_MESSAGE"
              @saveHandle="setColumn"
            />
          </div>
        </div>

        <div v-if="showAllInputFilter" class="flex-box table-header-row">
          <el-form-item>
            <div class="flex-box">
              <el-date-picker
                class="filter-item"
                v-model="timeDuration"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                start-placeholder="创建时间"
                end-placeholder="创建时间"
                clearable
                @change="getDataList"
                unlink-panels
              />
            </div>
          </el-form-item>
          <el-form-item>
            <div class="flex-box ml-8">
              <el-select
                v-model="currentHandlerValues"
                placeholder="请选择当前处理人"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip="true"
                :max-collapse-tags="1"
                @change="getDataList"
                filterable
                class="filter-item"
              >
                <el-option
                  v-for="item in currentPersonOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item class="ml-8 select-product">
            <el-select
              v-model="filterObj.productType"
              clearable
              placeholder="请选择产品类型"
              class="select-left"
              style="margin-right: -1px"
              @change="productTypeChange"
            >
              <el-option
                v-for="item in productTypeList"
                :label="item"
                :value="item"
                :key="item"
              />
            </el-select>
            <el-select
              v-model="productModelList"
              multiple
              class="select-right next"
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              clearable
              no-data-text="请先选择产品类型"
              :placeholder="
                !filterObj.productType ? '请选择产品机型' : '请选择产品机型'
              "
              @change="productModelChange"
            >
              <el-option
                style="flex-wrap: nowrap; display: flex"
                v-for="item in productMap[filterObj.productType]"
                :key="item"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
    </div>
    <!-- 表格部分 -->
    <div class="search-result-table">
      <div
        class="table-wrap"
        style="width: 100%; overflow-x: auto; box-sizing: border-box"
      >
        <el-table
          :data="tableData"
          @sort-change="sortChange"
          :default-sort="{ prop: 'totalVolume', order: 'descending' }"
          style="width: 100%"
          :header-cell-class-name="(params:any) => {setHeaderClass(params)}"
          v-loading="loading"
          :empty-text="TABLE_EMPTY_TXT"
        >
          <el-table-column
            v-for="item in columnList"
            show-overflow-tooltip
            :key="item.prop"
            :type="item.type"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            :fixed="item.fixed"
            :minWidth="item.minWidth"
            :sortable="item.sortable"
            :sort-orders="item.sortOrders"
          >
            <template #header>
              <template v-if="item.tooltip">
                <span>
                  {{ item.label }}
                </span>
                <el-tooltip :content="item.tooltip" placement="top">
                  <span>
                    <el-icon class="question-icon"><QuestionFilled /></el-icon
                  ></span>
                </el-tooltip>
              </template>
              <span v-else>{{ item.label }}</span>
            </template>
            <template #default="scope">
              <div v-if="item.longText">
                <el-tooltip
                  style="width: 350px"
                  effect="dark"
                  :content="scope.row[item.prop]"
                  placement="top-end"
                >
                  <span
                    style="
                      display: -webkit-box;
                      -webkit-box-orient: vertical;
                      -webkit-line-clamp: 1;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    "
                    >{{ scope.row[item.prop] }}</span
                  >
                </el-tooltip>
              </div>
              <div v-if="item.placeholder">
                {{ scope.row[item.prop] || item.placeholder }}
              </div>
              <div v-if="item.transTime">
                {{ transTime(scope.row[item.prop]) }}
              </div>
              <div v-else-if="item.prop === 'config'">
                <span class="primary-font" @click="gotoDetail(scope, 'info')"
                  >查看详情</span
                >
                <span
                  @click="gotoDetail(scope, 'handle')"
                  class="ml-8 primary-font"
                  v-if="hasProcessPublicOpinion"
                  >问题处理</span
                >
                <span
                  @click="delProblemData(scope.row)"
                  class="ml-8 primary-font"
                  v-if="hasDelPublicOpinion"
                  >删除</span
                >
              </div>
              <div v-else-if="item.prop === 'progress'">
                <div
                  v-if="!scope.row.showEdit"
                  @dblclick="showProgressEdit(scope.row)"
                >
                  {{ scope.row[item.prop] || "---" }}
                </div>
                <div v-else class="progress-edit-wrap">
                  <el-input
                    type="textarea"
                    placeholder="请输入问题进展"
                    v-model="scope.row[item.prop]"
                    show-word-limit
                    maxlength="2000"
                    @blur="closeProgressBlur(scope.row)"
                  />
                </div>
              </div>
              <div v-else-if="item.prop !== 'checkbox'">
                {{ scope.row[item.prop] || "---" }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex-box j-c-space-between a-i-center mt-12">
          <span class="pagination-total">{{ `总计：${pageInfo.total}` }}</span>
          <div class="flex-box a-i-center">
            <el-pagination
              v-model:current-page="pageInfo.pageNo"
              background
              size="small"
              :page-size="pageInfo.pageSize"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="pageSizes"
              :total="pageInfo.total"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import router from "@/router/index";
import { CaretBottom, CaretTop } from "@element-plus/icons-vue";
import { ewpService as service } from "@/utils/axios";
import { ref, onMounted, computed, onActivated } from "vue";
import searchArea from "../commonArea/serachArea.vue";
import { transTime } from "../commonArea/commonMethods";
import {
  defaulList,
  otherList,
  transValue2Label,
  statusList,
  productMap,
} from "../commonArea/checkList";
import { useUserStore } from "@/store/modules/user";
import { defaultColumn, allColumn } from "./index";
import ColumnSettingDialog from "@/components/columnSettingsDialog/index.vue";
import { LocalStorageEnum } from "@/components/columnSettingsDialog/index";
import { storage } from "@/utils/Storage";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
import { QuestionFilled } from "@element-plus/icons-vue";
import { isDev, WO_AUTH } from "@/utils/env";
import { pageSizes, TABLE_EMPTY_TXT } from "@/utils/constant";

const userStore = useUserStore();
const userInfo = userStore;
const exportLoading = ref(false);
const sortField = ref(["totalVolume", "priority"]);
const sortOrder = ref(["descending", "descending"]);
const route = useRoute();
// 排序
const sortChange = (e) => {
  const index = sortField.value.indexOf(e.prop);
  if (index >= 0) {
    if (e.order) {
      sortField.value[index] = e.prop;
      sortOrder.value[index] = e.order;
    } else {
      sortField.value.splice(index, 1);
      sortOrder.value.splice(index, 1);
    }
  } else {
    sortField.value.push(e.prop);
    sortOrder.value.push(e.order);
  }
  getDataList();
};

const setHeaderClass = (params: any) => {
  const index = sortField.value.indexOf(params.column.property);
  if (index >= 0) {
    params.column.order = sortOrder.value[index];
  }
};

// 判断是否创建问题权限
const hasProcessPublicOpinion = computed(() => {
  return checkResourcePermission("ProcessPublicOpinion");
});
// 判断是否删除问题权限
const hasDelPublicOpinion = computed(() => {
  return checkResourcePermission("DeletePublicOpinion");
});
const timeDuration = ref([]);
const loading = ref(false);
const getDefaultFilterOptionVals = () => {
  const res = {};
  defaulList.concat(otherList).forEach((item) => {
    res[item.key] = ["all"];
  });
  return res;
};

const filterOptions = ref(getDefaultFilterOptionVals());
const handleFilterOptions = (value) => {
  filterOptions.value = value;
  getDataList();
};
const pageInfo = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});

// 问题查询接口，筛选条件传参
const getFilterParams = () => {
  const res = {};
  Object.keys(filterOptions.value).forEach((key) => {
    if (!filterOptions.value[key].includes("all")) {
      res[key] = filterOptions.value[key].join(",");
    }
  });
  Object.keys(filterObj.value).forEach((key) => {
    res[key] = filterObj.value[key];
  });
  return res;
};

const tableData = ref([]);
const columnList = ref([]);
const setColumn = (columnData) => {
  columnList.value = [
    { prop: "checkbox", label: "", width: "40px", type: "selection" },
    ...cloneDeep(columnData),
    {
      prop: "config",
      label: "操作",
      width: hasDelPublicOpinion ? "190px" : "150px",
      fixed: "right",
    },
  ];
};
const gotoDetail = (scope, type) => {
  router.push({
    path: "/opinion-management-detail",
    query: { id: scope.row.opinionIssueId, type },
  });
};
const filterObj = ref({
  appName: "",
  startCreateTime: "",
  endCreateTime: "",
  productType: "",
  productModel: "",
  description: "",
});
const productModelList = ref([]);
const showAllInputFilter = ref(false);
const formInline = {
  user: "",
  radio1: "",
  date: "",
};

const productModelChange = () => {
  filterObj.value.productModel = productModelList.value.join(",");
  getDataList();
};
const productTypeChange = () => {
  filterObj.value.productModel = "";
  productModelList.value = [];
  getDataList();
};
const currentHandlerValues = ref([]);
const currentPersonOptions = ref([{ value: "", label: "" }]); // 所有的当前处理人
const setCurrentPersionSelect = (personList = []) => {
  const options = [];
  personList.forEach((per) => {
    options.push({ label: per, value: per });
  });
  currentPersonOptions.value = options;
};

onMounted(() => {
  // 从URL参数获取应用名称
  const appNameFromUrl = route.query.appName as string;
  if (appNameFromUrl) {
    filterObj.value.appName = appNameFromUrl;
  }
  getDataList();
  // 设置动态列
  const localStorageClounm = storage.get(
    LocalStorageEnum.ISSUES_OPTIONION_MESSAGE
  );
  if (localStorageClounm) {
    setColumn(localStorageClounm);
  } else {
    setColumn(defaultColumn);
  }
});
const resetSearch = () => {
  pageInfo.value.pageNo = 1;
  pageInfo.value.pageSize = 10;
  Object.keys(filterObj.value).forEach((key) => {
    filterObj.value[key] = "";
  });
  productModelList.value = [];
  timeDuration.value = [];
  filterOptions.value = getDefaultFilterOptionVals();
  currentHandlerValues.value = [];
  // 排序重置
  sortField.value = ["totalVolume", "priority"];
  sortOrder.value = ["descending", "descending"];
  getDataList();
};
const handleSizeChange = (num) => {
  pageInfo.value.pageNo = 1;
  pageInfo.value.pageSize = num;
  getDataList();
};
const handleCurrentChange = (num) => {
  pageInfo.value.pageNo = num;
  getDataList();
};
const getDataList = async () => {
  loading.value = true;
  try {
    const data = {
      pageNo: pageInfo.value.pageNo,
      pageSize: pageInfo.value.pageSize,
      ...getFilterParams(),
      startCreateTime: timeDuration.value ? timeDuration.value[0] : "",
      endCreateTime: timeDuration.value ? timeDuration.value[1] : "",
      currentHandlerList: currentHandlerValues.value,
      sortField: sortField.value,
      sortOrder: sortOrder.value,
    };
    const res = await service.post(`/opinionIssue/query`, data);
    res?.flatDataList.forEach((item) => {
      item.status = transValue2Label(item.status, statusList);
    });
    tableData.value = res.flatDataList || [];
    pageInfo.value.total = res.total || 0;
    setCurrentPersionSelect(res?.currentHandlerList);
    loading.value = false;
  } catch (error) {
    console.error("Error fetching work order list:", error);
    loading.value = false;
    throw error;
  }
};
// 通用权限检查函数
const checkResourcePermission = (resourceId: string) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};
// 判断是否有导出权限
const hasExportPermission = computed(() => {
  return checkResourcePermission("ExportPublicOpinion");
});
const productTypeList = computed(() => {
  return Object.keys(productMap);
});
const handleExportRes = () => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    exportLoading.value = true;
    const queryParams = {
      pageNo: 1,
      pageSize: 99999,
      ...getFilterParams(),
      startCreateTime: timeDuration.value ? timeDuration.value[0] : "",
      endCreateTime: timeDuration.value ? timeDuration.value[1] : "",
      currentHandlerList: currentHandlerValues.value,
      sortField: sortField.value,
      sortOrder: sortOrder.value,
    };
    req.open(
      "POST",
      `${window.location.origin}${
        import.meta.env.VITE_APP_BASE_API
      }/opinionIssue/exportData`,
      true
    );
    req.responseType = "blob";
    req.setRequestHeader("Content-Type", "application/json");
    // 使用新的 CSRFTokenManager 获取 CSRF 令牌
    const csrfToken = csrfTokenManager.getToken();
    if (csrfToken) {
      req.setRequestHeader("X-CSRF-TOKEN", csrfToken);
    }
    if (isDev()) {
      req.setRequestHeader("token", atob(WO_AUTH));
    }
    req.onload = function () {
      const data = req.response;
      if (req.status === 413) {
        exportLoading.value = false;
        ElMessage.error("导出数据量过大，限制50000条。请修改查询条件后重试");
        reject();
        return;
      }

      if (data.size === 0) {
        exportLoading.value = false;
        ElMessage.error("没有数据可导出");
        reject();
        return;
      }
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.download = "问题管理导出.xlsx";
      a.href = blobUrl;
      a.click();
      exportLoading.value = false;
      resolve(true);
    };
    req.send(JSON.stringify(queryParams));
  });
};

// 根据问题id查询是否当前人是否有编辑问题进展权限
const hasEditProgressPermissions = async (opinionIssueId) => {
  let result = false;
  try {
    const response = (await service.get(
      `/lockResolvePlan/hasPermission?opinionIssueId=${opinionIssueId}`
    )) as boolean;
    result = response;
  } catch (e) {
    console.error("没有处理该应用进展的权限", e?.msg);
    throw e;
  }
  return result;
};

const lastProgressObj = {
  opinionIssueId: "",
  progress: "",
};
const showProgressEdit = async (row) => {
  lastProgressObj.opinionIssueId = row.opinionIssueId;
  lastProgressObj.progress = row.progress;
  await hasEditProgressPermissions(row.opinionIssueId);
  row.showEdit = true;
};

// 保存问题进展
const closeProgressBlur = async (row) => {
  try {
    if (row.progress && row.progress.trim()) {
      const data = {
        opinionIssueId: row.opinionIssueId,
        progress: row.progress,
      };
      await service.post(`/lockResolvePlan/saveProgress`, data);
      console.log("修改应用进展成功");
    } else {
      if (row.opinionIssueId === lastProgressObj.opinionIssueId) {
        row.progress = lastProgressObj.progress || "---";
      }
    }
  } catch (e) {
    if (row.opinionIssueId === lastProgressObj.opinionIssueId) {
      row.progress = lastProgressObj.progress || "---";
    }
    ElMessage.error(`修改应用进展失败 ${e?.msg}`);
    throw e;
  }
  lastProgressObj.opinionIssueId = "";
  lastProgressObj.progress = "";
  row.showEdit = false;
};
onActivated(() => {
  getDataList();
});
/**
 * 删除问题
 * @param rowData 行内数据
 */
const delProblemData = (rowData) => {
  // 确认删除
  ElMessageBox.confirm("确认删除该问题？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await service.post(`/opinionIssue/delete/${rowData.opinionIssueId}`);
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      pageInfo.value.pageNo = 1;
      getDataList();
    } catch (error) {
      ElMessage({
        type: "error",
        message: error.msg || "删除数据失败!",
      });
    } finally {
      loading.value = false;
    }
  });
};
</script>

<style lang="scss" scoped>
.opinion-manage {
  .ml-8 {
    margin-left: 8px;
  }
  .mt-8 {
    margin-top: 8px;
  }
  .table-head {
    display: flex;
    justify-content: space-between;
  }

  .table-head-left {
    display: flex;
  }
  .progress-edit-wrap {
    z-index: 999;
    top: 0px;
    background-color: #fff;
  }
}
.question-icon {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
  cursor: pointer;
}
</style>
