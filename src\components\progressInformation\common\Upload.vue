<template>
  <el-upload
    v-model:file-list="fileList"
    class="upload"
    :action="uploadURL"
    multiple
    :on-preview="handlePreview"
    :on-remove="handleRemove"
    :before-remove="beforeRemove"
    :limit="10"
    :on-exceed="handleExceed"
    :before-upload="beforeUpload"
    :data="getUploadParams"
    :headers="headers"
    :on-success="handleSuccess"
    :disabled="!canEdit"
  >
    <el-button v-if="canEdit" :icon="Upload">上传附件</el-button>
    <template #tip>
      <div id="tip">{{ `格式仅支持${suportedSuffixList.join(',')}` }}</div>
    </template>
  </el-upload>
</template>
 
<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
 
import type { UploadProps, UploadUserFile } from 'element-plus'
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { getAuthCode } from '@/business/progressInformation/common/upload'
import { isDev, WO_AUTH } from "@/utils/env";
 
const props = defineProps({
  uploadFileParams: {
    required: true,
    default: () => [],
  },
  canEdit: {
    default: () => false,
    required: true,
  },
});
 
defineExpose({ getFileListParams })
 
const uploadURL = `${import.meta.env.VITE_APP_BASE_API}/fileUpload/attachment`
 
const suportedSuffixList = ['png', 'jpg', 'jpeg', 'docx', 'pdf', 'xlsx', 'msg', 'zip', 'gif', 'mp4', 'mov', 'wmv']
 
let uploadParams = {}
 
const getUploadParams = () => {
  return uploadParams
}
 
const headers = {}
const csrfToken = csrfTokenManager.getToken();
if (csrfToken) {
  headers["X-CSRF-TOKEN"] = csrfToken
}
if (isDev()) {
  headers["token"] = atob(WO_AUTH)
}
 
const fileList = ref<UploadUserFile[]>([])
 
const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  console.log(file, uploadFiles)
}
 
const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
  window.open(uploadFile.url)
}
 
const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  ElMessage.warning('最多上传10个附件')
}
 
const beforeRemove: UploadProps['beforeRemove'] = (uploadFile, uploadFiles) => {
  if(uploadFile.status === 'ready') {
    return true
  }
  return ElMessageBox.confirm(
    `确认删除【${uploadFile.name}】?`
  ).then(
    () => true,
    () => false
  )
}
 
const beforeUpload: UploadProps['beforeUpload'] = async (uploadFile) => {
  const isExceeds = uploadFile.size > (512 * 1024 * 1024); // 限制为512MB
  if (isExceeds) {
    ElMessage.warning('上传文件大小不能超过 512MB!');
    return false;
  }
  const suffix = uploadFile.name.split('.').pop().toLowerCase()
  const isSuported = suportedSuffixList.includes(suffix)
  if(!isSuported) {
    ElMessage.warning(`格式仅支持${suportedSuffixList.join(',')}`);
    return false;
  }
  try {
    const response = await getAuthCode({
      filePostfix: suffix,
      scene: 'VOC_UPLOAD_ATTACHMENT'
    })
    if(response) {
      uploadParams = {
        fileName: uploadFile.name,
        fileCount: 1,
        authCode: response,
        name: uploadFile.name,
      }
      return true
    } else {
      return false
    }
  } catch (err) {
    ElMessage.error('上传失败，请稍后再试')
    console.log(err)
    return false
  }
}
 
const handleSuccess = (response) => {
  if(response.code === 200) {
    const lastIndex = fileList.value.length - 1
    fileList.value[lastIndex].url = response.data
  } else {
    ElMessage.error('上传失败，请稍后再试')
    console.log(response)
    fileList.value.pop()
  }
}
 
function getFileListParams() {
  return fileList.value.map(item => {
    return {
      id: item.id,
      fileName: item.name,
      fileExtension: item.name.split('.').pop().toLowerCase(),
      fileSize: item.size,
      filePath: item.url
    }
  })
}
 
watchEffect(() => {
  fileList.value = props.uploadFileParams.map(item => {
    return {
      id: item.id,
      name: item.fileName,
      size: item.fileSize,
      url: item.filePath
    }
  })
})
</script>
<style lang="scss" scoped>
#tip {
  margin-top: 12px;
  color: rgb(153, 153, 153);
  font-family: 微软雅黑;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0%;
  text-align: left;
}
</style>