import path from 'node:path'
import Vue from '@vitejs/plugin-vue'

import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import VueRouter from 'unplugin-vue-router/vite'

import { defineConfig } from 'vite'

export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? './' : '/',
  resolve: {
    alias: {
      '~/': `${path.resolve(__dirname, 'src')}/`,
      '@/': `${path.resolve(__dirname, 'src')}/`,
    },
  },

  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "~/styles/element/index.scss" as *;`,
        api: 'modern-compiler',
      },
    },
  },

  plugins: [
    Vue(),

    VueRouter({
      extensions: ['.vue', '.md'],
      dts: 'src/typed-router.d.ts',
    }),

    Components({
      extensions: ['vue', 'md'],
      include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
      dts: 'src/components.d.ts',
    }),
  ],
  server: {
    host: true,
    proxy: {
      '/board': {
        target: 'http://***********',
        changeOrigin: true,
      },
      '/voc-dev/3rd-eco/issue-mgr/backend': {
        // target: 'http://*************:8084', // ELB
        target: 'https://lfwiseopertest04.hwcloudtest.cn/', // ELB，要开VPN
        // target: 'http://************:8882', // 金圣日
        // target: 'http://**************:8882', // liuwei
        // target: 'http://************:8882', // 潘淑华
        changeOrigin: true,
      },
    },
  },
})
